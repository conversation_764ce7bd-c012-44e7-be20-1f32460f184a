<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{post.title}} - Retrospxt Holdings LLC</title>
    <meta name="description" content="{{post.excerpt}}">
    <meta name="keywords" content="{{post.tags}}">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="{{post.url}}">
    <meta property="og:title" content="{{post.title}}">
    <meta property="og:description" content="{{post.excerpt}}">
    <meta property="og:image" content="{{post.featured_image}}">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{post.url}}">
    <meta property="twitter:title" content="{{post.title}}">
    <meta property="twitter:description" content="{{post.excerpt}}">
    <meta property="twitter:image" content="{{post.featured_image}}">
    
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/blog.css">
    <link rel="canonical" href="{{post.url}}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": "{{post.title}}",
        "description": "{{post.excerpt}}",
        "image": "{{post.featured_image}}",
        "author": {
            "@type": "Organization",
            "name": "Retrospxt Holdings LLC"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Retrospxt Holdings LLC",
            "logo": {
                "@type": "ImageObject",
                "url": "{{site.logo}}"
            }
        },
        "datePublished": "{{post.published_date}}",
        "dateModified": "{{post.modified_date}}"
    }
    </script>
</head>
<body>
    <!-- Navigation Component -->
    <nav class="glass-nav" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html">
                    <h2>RETROSPXT</h2>
                    <span>HOLDINGS, LLC</span>
                </a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#home" class="nav-link">Home</a>
                <a href="../index.html#services" class="nav-link">Services</a>
                <a href="../index.html#training" class="nav-link">Training</a>
                <a href="../index.html#community" class="nav-link">Community</a>
                <a href="index.html" class="nav-link active">Blog</a>
                <a href="../index.html#newsletter" class="nav-link">Newsletter</a>
                <a href="../index.html#about" class="nav-link">About</a>
            </div>
        </div>
    </nav>

    <!-- Article Header Component -->
    <article class="blog-post">
        <header class="post-header">
            <div class="container">
                <div class="post-breadcrumb">
                    <a href="../index.html">Home</a> / 
                    <a href="index.html">Blog</a> / 
                    <span>{{post.title}}</span>
                </div>
                
                <div class="post-meta">
                    <span class="post-category">{{post.category}}</span>
                    <time datetime="{{post.published_date}}">{{post.published_date_formatted}}</time>
                    <span class="post-read-time">{{post.read_time}} min read</span>
                </div>
                
                <h1 class="post-title">{{post.title}}</h1>
                <p class="post-excerpt">{{post.excerpt}}</p>
                
                <div class="post-author">
                    <img src="{{post.author.avatar}}" alt="{{post.author.name}}" class="author-avatar">
                    <div class="author-info">
                        <span class="author-name">{{post.author.name}}</span>
                        <span class="author-title">{{post.author.title}}</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Featured Image Component -->
        <div class="post-featured-image">
            <img src="{{post.featured_image}}" alt="{{post.title}}" loading="lazy">
        </div>

        <!-- Article Content Component -->
        <div class="post-content">
            <div class="container">
                <div class="content-wrapper">
                    <!-- Table of Contents Component -->
                    <aside class="table-of-contents glass-card">
                        <h3>Table of Contents</h3>
                        <nav id="toc">
                            <!-- Auto-generated from headings -->
                        </nav>
                    </aside>

                    <!-- Main Content -->
                    <main class="post-body">
                        {{post.content}}
                        
                        <!-- Tags Component -->
                        <div class="post-tags">
                            <h4>Tags:</h4>
                            {{#each post.tags}}
                            <span class="tag">{{this}}</span>
                            {{/each}}
                        </div>
                    </main>
                </div>
            </div>
        </div>

        <!-- Social Share Component -->
        <div class="social-share">
            <div class="container">
                <h3>Share this article</h3>
                <div class="share-buttons">
                    <button class="share-btn twitter" onclick="shareOnTwitter()">
                        <svg><!-- Twitter icon --></svg>
                        Twitter
                    </button>
                    <button class="share-btn linkedin" onclick="shareOnLinkedIn()">
                        <svg><!-- LinkedIn icon --></svg>
                        LinkedIn
                    </button>
                    <button class="share-btn copy" onclick="copyLink()">
                        <svg><!-- Copy icon --></svg>
                        Copy Link
                    </button>
                </div>
            </div>
        </div>

        <!-- Related Posts Component -->
        <section class="related-posts">
            <div class="container">
                <h2>Related Articles</h2>
                <div class="related-grid" id="related-posts">
                    <!-- Populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Newsletter CTA Component -->
        <section class="newsletter-cta glass-card">
            <div class="container">
                <h2>Stay Updated</h2>
                <p>Get weekly insights delivered to your inbox</p>
                <form class="inline-newsletter-form">
                    <input type="email" placeholder="Enter your email" required>
                    <button type="submit" class="btn-primary">Subscribe</button>
                </form>
            </div>
        </section>
    </article>

    <script src="../js/script.js"></script>
    <script src="../js/blog-post.js"></script>
</body>
</html>