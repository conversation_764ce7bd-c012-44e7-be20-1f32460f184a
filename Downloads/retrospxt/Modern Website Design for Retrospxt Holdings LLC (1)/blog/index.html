<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - Retrospxt Holdings LLC</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/blog.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="glass-nav" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html">
                    <h2>RETROSPXT</h2>
                    <span>HOLDINGS, LLC</span>
                </a>
            </div>
            <div class="nav-menu">
                <a href="../index.html#home" class="nav-link">Home</a>
                <a href="../index.html#services" class="nav-link">Services</a>
                <a href="../index.html#training" class="nav-link">Training</a>
                <a href="../index.html#community" class="nav-link">Community</a>
                <a href="index.html" class="nav-link active">Blog</a>
                <a href="../index.html#newsletter" class="nav-link">Newsletter</a>
                <a href="../index.html#about" class="nav-link">About</a>
            </div>
        </div>
    </nav>

    <!-- Blog Header -->
    <section class="blog-header">
        <div class="container">
            <h1>The Priceless & The Pointless</h1>
            <p>Cutting through AI hype to deliver actionable insights for your business</p>
        </div>
    </section>

    <!-- Blog Posts -->
    <section class="blog-posts">
        <div class="container">
            <div class="blog-filters">
                <button class="filter-btn active" data-filter="all">All Posts</button>
                <button class="filter-btn" data-filter="ai-strategy">AI Strategy</button>
                <button class="filter-btn" data-filter="implementation">Implementation</button>
                <button class="filter-btn" data-filter="business-impact">Business Impact</button>
            </div>
            
            <div class="blog-grid" id="blog-posts">
                <!-- Blog posts will be loaded here -->
            </div>
        </div>
    </section>

    <script src="../js/script.js"></script>
    <script src="../js/blog.js"></script>
</body>
</html>