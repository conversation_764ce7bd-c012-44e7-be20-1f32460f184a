/* Blog Section */
.blog {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(0, 123, 255, 0.1) 100%);
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
}

.blog-card {
    overflow: hidden;
    transition: all var(--transition-normal);
}

.blog-card:hover {
    transform: translateY(-4px);
}

.blog-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-content {
    padding: var(--spacing-6);
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
}

.blog-category {
    background: linear-gradient(135deg, var(--primary-blue), var(--electric-blue));
    color: white;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: 20px;
    font-weight: 600;
}

.blog-date {
    color: var(--text-muted);
}

.blog-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--text-primary);
    line-height: 1.4;
}

.blog-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.blog-link {
    color: var(--electric-blue);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-normal);
}

.blog-link:hover {
    color: var(--primary-blue);
}

.blog-cta {
    text-align: center;
}