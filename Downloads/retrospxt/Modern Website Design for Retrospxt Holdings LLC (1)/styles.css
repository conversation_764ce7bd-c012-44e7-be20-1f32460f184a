/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-blue: #007BFF;
    --electric-blue: #00D4FF;
    --deep-space: #0A1628;
    --dark-blue: #1a2332;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --text-primary: #FFFFFF;
    --text-secondary: #E2E8F0;
    --text-muted: #94A3B8;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--deep-space) 0%, var(--dark-blue) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Glass Morphism Base Classes */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--shadow-glass);
    transition: all var(--transition-normal);
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

.glass-nav {
    background: rgba(10, 22, 40, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.glass-form {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: var(--spacing-6);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Navigation */
.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-6);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo h2 {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--text-primary);
    letter-spacing: -0.02em;
}

.nav-logo span {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 400;
    letter-spacing: 0.1em;
}

.nav-menu {
    display: flex;
    gap: var(--spacing-8);
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--electric-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-blue), var(--electric-blue));
    transition: width var(--transition-normal);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-cta {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: all var(--transition-normal);
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-outline {
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: 8px;
    font-weight: 600;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--electric-blue));
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--primary-blue);
    color: var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.btn-primary.large, .btn-secondary.large {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-base);
}

.full-width {
    width: 100%;
    justify-content: center;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../assets/images/hero-background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.3;
    z-index: -1;
}

.hero-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(10, 22, 40, 0.8) 0%, rgba(26, 35, 50, 0.6) 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--spacing-6);
    letter-spacing: -0.02em;
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-blue), var(--electric-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.floating-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: var(--spacing-6);
    animation: float 6s ease-in-out infinite;
    box-shadow: var(--shadow-glass);
}

.card-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    color: var(--text-primary);
    font-weight: 600;
}

.ai-indicator {
    width: 12px;
    height: 12px;
    background: var(--electric-blue);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-8);
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 24px;
    height: 24px;
    border-right: 2px solid var(--text-secondary);
    border-bottom: 2px solid var(--text-secondary);
    transform: rotate(45deg);
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Sections */
section {
    padding: var(--spacing-24) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    letter-spacing: -0.02em;
}

.section-header p {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--electric-blue);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
}

/* Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.service-card {
    padding: var(--spacing-8);
    text-align: center;
    transition: all var(--transition-normal);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--glass-bg);
    border-radius: 20px;
    border: 1px solid var(--glass-border);
}

.service-icon img {
    width: 48px;
    height: 48px;
}

.service-card h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-6);
    line-height: 1.6;
}

.service-features {
    list-style: none;
    margin-bottom: var(--spacing-6);
    text-align: left;
}

.service-features li {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2);
    position: relative;
    padding-left: var(--spacing-6);
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--electric-blue);
    font-weight: bold;
}

/* Training Section */
.training {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 212, 255, 0.05) 100%);
}

.training-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.training-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    background: linear-gradient(135deg, var(--primary-blue), var(--electric-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.training-features {
    margin: var(--spacing-8) 0;
}

.feature-item {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    align-items: flex-start;
}

.feature-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
}

.feature-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--text-primary);
}

.feature-item p {
    color: var(--text-secondary);
}

.training-cta {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.training-stats {
    padding: var(--spacing-8);
}

.stat-item {
    text-align: center;
    margin-bottom: var(--spacing-6);
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--electric-blue);
    margin-bottom: var(--spacing-2);
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Newsletter Section */
.newsletter {
    background: linear-gradient(135deg, rgba(26, 35, 50, 0.8) 0%, rgba(10, 22, 40, 0.9) 100%);
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    padding: var(--spacing-12);
    align-items: center;
}

.newsletter-text h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    background: linear-gradient(135deg, var(--primary-blue), var(--electric-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.newsletter-benefits {
    margin-top: var(--spacing-6);
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-3);
    color: var(--text-secondary);
}

.benefit-icon {
    font-size: var(--font-size-lg);
}

.newsletter-form h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-6);
    text-align: center;
    color: var(--text-primary);
}

/* Forms */
.form-group {
    position: relative;
    margin-bottom: var(--spacing-6);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-4);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group label {
    position: absolute;
    top: var(--spacing-4);
    left: var(--spacing-4);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    pointer-events: none;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group select:focus + label,
.form-group select:not([value=""]) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label {
    top: -8px;
    left: var(--spacing-3);
    font-size: var(--font-size-xs);
    color: var(--primary-blue);
    background: var(--deep-space);
    padding: 0 var(--spacing-2);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    cursor: pointer;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.form-note {
    text-align: center;
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-4);
}

/* Community Section */
.community-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-12);
}

.community-benefit {
    padding: var(--spacing-6);
    text-align: center;
}

.community-benefit .benefit-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
    display: block;
}

.community-benefit h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--text-primary);
}

.community-benefit p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.community-cta {
    text-align: center;
}

.community-note {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-4);
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-16);
    align-items: start;
}

.about-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
}

.about-text p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-6);
    line-height: 1.7;
    font-size: var(--font-size-lg);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-4);
    margin-top: var(--spacing-8);
}

.stat-card {
    padding: var(--spacing-6);
    text-align: center;
}

.vision-card {
    padding: var(--spacing-8);
}

.vision-card h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    color: var(--electric-blue);
}

.vision-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, rgba(10, 22, 40, 0.95) 0%, rgba(26, 35, 50, 0.9) 100%);
    border-top: 1px solid var(--glass-border);
    padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: var(--spacing-16);
    margin-bottom: var(--spacing-12);
}

.footer-brand h3 {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: var(--spacing-2);
}

.footer-brand span {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 400;
    letter-spacing: 0.1em;
    display: block;
    margin-bottom: var(--spacing-4);
}

.footer-brand p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-8);
}

.footer-column h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-4);
}

.footer-column a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: var(--spacing-2);
    transition: color var(--transition-fast);
}

.footer-column a:hover {
    color: var(--electric-blue);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-8);
    border-top: 1px solid var(--glass-border);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.footer-legal {
    display: flex;
    gap: var(--spacing-6);
}

.footer-legal a {
    color: var(--text-muted);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-legal a:hover {
    color: var(--electric-blue);
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    margin: 5% auto;
    padding: var(--spacing-8);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

.modal-content h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    text-align: center;
    color: var(--text-primary);
}

.modal-content p {
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.close {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-6);
    color: var(--text-secondary);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.close:hover {
    color: var(--text-primary);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
        text-align: center;
    }
    
    .training-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
    }
    
    .newsletter-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
    }
    
    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-cta {
        display: none;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .about-stats {
        grid-template-columns: 1fr;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-4);
        text-align: center;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
        padding: var(--spacing-6);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .section-header h2 {
        font-size: var(--font-size-3xl);
    }
    
    .btn-primary.large, .btn-secondary.large {
        padding: var(--spacing-3) var(--spacing-6);
        font-size: var(--font-size-sm);
    }
    
    .community-grid {
        grid-template-columns: 1fr;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .scroll-indicator {
        animation: none;
    }
    
    .floating-card {
        animation: none;
    }
    
    .ai-indicator {
        animation: none;
    }
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.2);
        --glass-border: rgba(255, 255, 255, 0.4);
        --text-secondary: #F1F5F9;
    }
}

