// Blog Post Renderer
class BlogPostRenderer {
    constructor() {
        this.cms = window.NotionCMS;
        this.init();
    }

    async init() {
        const slug = this.getSlugFromURL();
        if (!slug) return;

        try {
            const post = await this.cms.fetchPost(slug);
            if (!post) {
                this.show404();
                return;
            }

            this.renderPost(post);
            this.setupTableOfContents();
            this.loadRelatedPosts(post);
            this.setupSocialShare(post);
        } catch (error) {
            console.error('Error loading post:', error);
            this.show404();
        }
    }

    getSlugFromURL() {
        const path = window.location.pathname;
        const match = path.match(/\/blog\/(.+)\.html$/);
        return match ? match[1] : null;
    }

    renderPost(post) {
        // Update document title and meta
        document.title = `${post.title} - Retrospxt Holdings LLC`;
        this.updateMeta(post);

        // Render post content
        this.updateElement('.post-category', post.category);
        this.updateElement('.post-title', post.title);
        this.updateElement('.post-excerpt', post.excerpt);
        this.updateElement('.post-read-time', `${post.read_time} min read`);
        this.updateElement('.author-name', post.author.name);
        this.updateElement('.author-title', post.author.title);
        this.updateElement('.post-body', post.content);

        // Update images
        this.updateImage('.post-featured-image img', post.featured_image, post.title);
        this.updateImage('.author-avatar', post.author.avatar, post.author.name);

        // Update date
        const dateElement = document.querySelector('time');
        if (dateElement) {
            dateElement.textContent = post.published_date_formatted;
            dateElement.setAttribute('datetime', post.published_date);
        }

        // Render tags
        this.renderTags(post.tags);

        // Update breadcrumb
        this.updateElement('.post-breadcrumb span', post.title);
    }

    updateElement(selector, content) {
        const element = document.querySelector(selector);
        if (element) element.innerHTML = content;
    }

    updateImage(selector, src, alt) {
        const img = document.querySelector(selector);
        if (img) {
            img.src = src;
            img.alt = alt;
        }
    }

    updateMeta(post) {
        // Update meta tags
        this.updateMetaTag('description', post.excerpt);
        this.updateMetaTag('og:title', post.title);
        this.updateMetaTag('og:description', post.excerpt);
        this.updateMetaTag('og:image', post.featured_image);
        this.updateMetaTag('twitter:title', post.title);
        this.updateMetaTag('twitter:description', post.excerpt);
        this.updateMetaTag('twitter:image', post.featured_image);

        // Update canonical URL
        const canonical = document.querySelector('link[rel="canonical"]');
        if (canonical) canonical.href = window.location.href;
    }

    updateMetaTag(property, content) {
        let selector = `meta[name="${property}"]`;
        if (property.startsWith('og:') || property.startsWith('twitter:')) {
            selector = `meta[property="${property}"]`;
        }
        
        const meta = document.querySelector(selector);
        if (meta) meta.content = content;
    }

    renderTags(tags) {
        const tagsContainer = document.querySelector('.post-tags');
        if (!tagsContainer || !tags.length) return;

        const tagsHTML = tags.map(tag => `<span class="tag">${tag}</span>`).join('');
        tagsContainer.innerHTML = `<h4>Tags:</h4>${tagsHTML}`;
    }

    setupTableOfContents() {
        const headings = document.querySelectorAll('.post-body h1, .post-body h2, .post-body h3');
        const toc = document.getElementById('toc');
        
        if (!toc || headings.length === 0) return;

        const tocHTML = Array.from(headings).map(heading => {
            const level = parseInt(heading.tagName.charAt(1));
            const text = heading.textContent;
            const id = heading.id || this.generateId(text);
            
            if (!heading.id) heading.id = id;
            
            return `<a href="#${id}" class="toc-link toc-level-${level}">${text}</a>`;
        }).join('');

        toc.innerHTML = tocHTML;

        // Smooth scroll for TOC links
        toc.addEventListener('click', (e) => {
            if (e.target.classList.contains('toc-link')) {
                e.preventDefault();
                const targetId = e.target.getAttribute('href').substring(1);
                const target = document.getElementById(targetId);
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }
        });
    }

    async loadRelatedPosts(currentPost) {
        try {
            const allPosts = await this.cms.fetchPosts();
            const relatedPosts = allPosts
                .filter(post => post.id !== currentPost.id)
                .filter(post => post.category === currentPost.category || 
                               post.tags.some(tag => currentPost.tags.includes(tag)))
                .slice(0, 3);

            this.renderRelatedPosts(relatedPosts);
        } catch (error) {
            console.error('Error loading related posts:', error);
        }
    }

    renderRelatedPosts(posts) {
        const container = document.getElementById('related-posts');
        if (!container || posts.length === 0) return;

        const postsHTML = posts.map(post => `
            <article class="blog-card glass-card">
                <div class="blog-image">
                    <img src="${post.featured_image}" alt="${post.title}" loading="lazy">
                </div>
                <div class="blog-content">
                    <div class="blog-meta">
                        <span class="blog-category">${post.category}</span>
                        <span class="blog-date">${post.published_date_formatted}</span>
                    </div>
                    <h3>${post.title}</h3>
                    <p>${post.excerpt}</p>
                    <a href="${post.url}" class="blog-link">Read More →</a>
                </div>
            </article>
        `).join('');

        container.innerHTML = postsHTML;
    }

    setupSocialShare(post) {
        window.shareOnTwitter = () => {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent(`${post.title} - ${post.excerpt}`);
            window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
        };

        window.shareOnLinkedIn = () => {
            const url = encodeURIComponent(window.location.href);
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
        };

        window.copyLink = () => {
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('Link copied to clipboard!');
            });
        };
    }

    generateId(text) {
        return text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    }

    show404() {
        document.body.innerHTML = `
            <div class="error-page">
                <h1>Post Not Found</h1>
                <p>The blog post you're looking for doesn't exist.</p>
                <a href="/blog/" class="btn-primary">Back to Blog</a>
            </div>
        `;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new BlogPostRenderer();
});