// Notion CMS Integration
class NotionCMS {
    constructor(config) {
        this.notionToken = config.notionToken;
        this.databaseId = config.databaseId;
        this.baseUrl = 'https://api.notion.com/v1';
        this.cache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    }

    // Fetch all blog posts from Notion
    async fetchPosts(filter = {}) {
        const cacheKey = `posts_${JSON.stringify(filter)}`;
        
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheExpiry) {
                return cached.data;
            }
        }

        try {
            const response = await fetch(`${this.baseUrl}/databases/${this.databaseId}/query`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.notionToken}`,
                    'Content-Type': 'application/json',
                    'Notion-Version': '2022-06-28'
                },
                body: JSON.stringify({
                    filter: this.buildFilter(filter),
                    sorts: [
                        {
                            property: 'Published Date',
                            direction: 'descending'
                        }
                    ]
                })
            });

            const data = await response.json();
            const posts = await Promise.all(
                data.results.map(page => this.transformNotionPage(page))
            );

            this.cache.set(cacheKey, {
                data: posts,
                timestamp: Date.now()
            });

            return posts;
        } catch (error) {
            console.error('Error fetching posts from Notion:', error);
            return [];
        }
    }

    // Fetch single post by slug
    async fetchPost(slug) {
        const cacheKey = `post_${slug}`;
        
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheExpiry) {
                return cached.data;
            }
        }

        try {
            const posts = await this.fetchPosts({
                property: 'Slug',
                rich_text: {
                    equals: slug
                }
            });

            if (posts.length === 0) return null;

            const post = posts[0];
            const content = await this.fetchPageContent(post.id);
            post.content = content;

            this.cache.set(cacheKey, {
                data: post,
                timestamp: Date.now()
            });

            return post;
        } catch (error) {
            console.error('Error fetching post:', error);
            return null;
        }
    }

    // Transform Notion page to blog post format
    async transformNotionPage(page) {
        const properties = page.properties;
        
        return {
            id: page.id,
            title: this.extractText(properties.Title),
            slug: this.extractText(properties.Slug),
            excerpt: this.extractText(properties.Excerpt),
            category: this.extractSelect(properties.Category),
            tags: this.extractMultiSelect(properties.Tags),
            published_date: this.extractDate(properties['Published Date']),
            published_date_formatted: this.formatDate(this.extractDate(properties['Published Date'])),
            modified_date: page.last_edited_time,
            featured_image: this.extractFile(properties['Featured Image']),
            author: {
                name: this.extractText(properties['Author Name']) || 'Retrospxt Holdings',
                title: this.extractText(properties['Author Title']) || 'AI Strategy Expert',
                avatar: this.extractFile(properties['Author Avatar']) || '/assets/images/default-avatar.jpg'
            },
            read_time: this.calculateReadTime(this.extractText(properties.Content)),
            status: this.extractSelect(properties.Status),
            url: `/blog/${this.extractText(properties.Slug)}.html`
        };
    }

    // Fetch page content blocks
    async fetchPageContent(pageId) {
        try {
            const response = await fetch(`${this.baseUrl}/blocks/${pageId}/children`, {
                headers: {
                    'Authorization': `Bearer ${this.notionToken}`,
                    'Notion-Version': '2022-06-28'
                }
            });

            const data = await response.json();
            return this.transformBlocksToHTML(data.results);
        } catch (error) {
            console.error('Error fetching page content:', error);
            return '';
        }
    }

    // Transform Notion blocks to HTML
    transformBlocksToHTML(blocks) {
        return blocks.map(block => {
            switch (block.type) {
                case 'paragraph':
                    return `<p>${this.transformRichText(block.paragraph.rich_text)}</p>`;
                case 'heading_1':
                    return `<h1 id="${this.generateId(block.heading_1.rich_text)}">${this.transformRichText(block.heading_1.rich_text)}</h1>`;
                case 'heading_2':
                    return `<h2 id="${this.generateId(block.heading_2.rich_text)}">${this.transformRichText(block.heading_2.rich_text)}</h2>`;
                case 'heading_3':
                    return `<h3 id="${this.generateId(block.heading_3.rich_text)}">${this.transformRichText(block.heading_3.rich_text)}</h3>`;
                case 'bulleted_list_item':
                    return `<li>${this.transformRichText(block.bulleted_list_item.rich_text)}</li>`;
                case 'numbered_list_item':
                    return `<li>${this.transformRichText(block.numbered_list_item.rich_text)}</li>`;
                case 'quote':
                    return `<blockquote>${this.transformRichText(block.quote.rich_text)}</blockquote>`;
                case 'code':
                    return `<pre><code class="language-${block.code.language}">${this.transformRichText(block.code.rich_text)}</code></pre>`;
                case 'image':
                    const imageUrl = block.image.file?.url || block.image.external?.url;
                    return `<img src="${imageUrl}" alt="${block.image.caption?.[0]?.plain_text || ''}" loading="lazy">`;
                default:
                    return '';
            }
        }).join('\n');
    }

    // Helper methods
    extractText(property) {
        if (!property) return '';
        if (property.type === 'title') return property.title?.[0]?.plain_text || '';
        if (property.type === 'rich_text') return property.rich_text?.[0]?.plain_text || '';
        return '';
    }

    extractSelect(property) {
        return property?.select?.name || '';
    }

    extractMultiSelect(property) {
        return property?.multi_select?.map(item => item.name) || [];
    }

    extractDate(property) {
        return property?.date?.start || '';
    }

    extractFile(property) {
        if (!property?.files?.[0]) return '';
        return property.files[0].file?.url || property.files[0].external?.url || '';
    }

    buildFilter(filter) {
        if (!filter.property) return {
            property: 'Status',
            select: {
                equals: 'Published'
            }
        };
        return filter;
    }

    calculateReadTime(text) {
        const wordsPerMinute = 200;
        const wordCount = text.split(' ').length;
        return Math.ceil(wordCount / wordsPerMinute);
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    generateId(richText) {
        const text = richText?.[0]?.plain_text || '';
        return text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    }

    transformRichText(richText) {
        return richText.map(text => {
            let content = text.plain_text;
            
            if (text.annotations.bold) content = `<strong>${content}</strong>`;
            if (text.annotations.italic) content = `<em>${content}</em>`;
            if (text.annotations.code) content = `<code>${content}</code>`;
            if (text.href) content = `<a href="${text.href}" target="_blank">${content}</a>`;
            
            return content;
        }).join('');
    }
}

// Initialize CMS
const cms = new NotionCMS({
    notionToken: 'YOUR_NOTION_INTEGRATION_TOKEN',
    databaseId: 'YOUR_NOTION_DATABASE_ID'
});

// Export for use in other files
window.NotionCMS = cms;